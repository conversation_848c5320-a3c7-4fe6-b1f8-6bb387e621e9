import {Builder, By, until, WebDriver, Key} from "selenium-webdriver";
import {Options} from "selenium-webdriver/chrome";
import { promises as fs } from 'fs';

async function createDriver() {
    const options = new Options();
    options.addArguments("--disable-blink-features=AutomationControlled")
    options.windowSize({width: 1280, height: 800})

    const driver = new Builder().forBrowser('chrome').setChromeOptions(options).build();
    return driver;

}


async function testing(driver: WebDriver) {
    await driver.get('https://www.google.com/');
    await driver.sleep(2000);
    await driver.findElement(By.name('q')).sendKeys('github.com/peyu5h', Key.RETURN);

    await driver.sleep(2000);
    await driver.findElement(By.xpath("//h3[normalize-space()='Piyush Bagul Peyu5h']")).click();
    await driver.sleep(2000);
    await driver.findElement(By.xpath("//a[normalize-space()='https://portfolio.peyu5h.tech']")).click();
    await driver.sleep(2000);
    const screenshot =  await driver.takeScreenshot();
    await fs.writeFile('screenshot.png', screenshot, 'base64');       
    await driver.sleep(3000);
}

async function test2(driver: WebDriver) {
    await driver.get("https://ui.shadcn.com/docs/components/input");
    await driver.sleep(2000);
    await driver.executeScript("scrollTo(0, document.body.scrollHeight)");
    await driver.sleep(2000);
    const input = await driver.findElement(By.css('input[name="username"]'));

    await input.sendKeys('peyu5h');
    await driver.findElement(By.xpath("//button[normalize-space()='Submit']")).click();
    await driver.sleep(5000);


}

async function main() {
    const driver = await createDriver();
    // await testing(driver);
    await test2(driver);
    await driver.quit();
}

main();