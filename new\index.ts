import {Builder, By, until, WebDriver, Key} from "selenium-webdriver";
import {Options} from "selenium-webdriver/chrome";

async function createDriver() {
    const options = new Options();
    options.addArguments("--disable-blink-features=AutomationControlled")

    const driver = new Builder().forBrowser('chrome').setChromeOptions(options).build();
    return driver;

}


async function testing(driver: WebDriver) {
    await driver.get('https://www.google.com/');
    await driver.sleep(2000);
    await driver.findElement(By.name('q')).sendKeys('github.com/peyu5h', Key.RETURN);

    await driver.sleep(2000);
    await driver.findElement(By.xpath("//h3[normalize-space()='Piyush Bagul Peyu5h']")).click();
    await driver.sleep(2000);
    await driver.findElement(By.xpath("//a[normalize-space()='https://portfolio.peyu5h.tech']")).click();
    await driver.sleep(2000);
    await driver.takeScreenshot();
    await driver.executeScript("window.scrollTo(0, 5000)");
    await driver.sleep(1000);
    await driver.executeScript("window.scrollTo(0, 5000)");
    await driver.sleep(1000);
    await driver.executeScript("window.scrollTo(0, 5000)");
    await driver.sleep(1000);
    await driver.executeScript("window.scrollTo(0,document.body.scrollHeight)");

    await driver.sleep(3000);
}

async function main() {
    const driver = await createDriver();
    await testing(driver);
    await driver.quit();
}

main();