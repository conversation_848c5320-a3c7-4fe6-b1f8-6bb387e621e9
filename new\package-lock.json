{"name": "new", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "new", "dependencies": {"@types/selenium-webdriver": "^4.1.28", "selenium-webdriver": "^4.35.0"}, "devDependencies": {"@types/bun": "latest", "@types/node": "^24.3.0"}, "peerDependencies": {"typescript": "^5"}}, "node_modules/@bazel/runfiles": {"version": "6.3.1", "license": "Apache-2.0"}, "node_modules/@types/bun": {"version": "1.2.20", "resolved": "https://registry.npmjs.org/@types/bun/-/bun-1.2.20.tgz", "integrity": "sha512-dX3RGzQ8+KgmMw7CsW4xT5ITBSCrSbfHc36SNT31EOUg/LA9JWq0VDdEXDRSe1InVWpd2yLUM1FUF/kEOyTzYA==", "dev": true, "license": "MIT", "dependencies": {"bun-types": "1.2.20"}}, "node_modules/@types/node": {"version": "24.3.0", "resolved": "https://registry.npmjs.org/@types/node/-/node-24.3.0.tgz", "integrity": "sha512-aPTXCrfwnDLj4VvXrm+UUCQjNEvJgNA8s5F1cvwQU+3KNltTOkBm1j30uNLyqqPNe7gE3KFzImYoZEfLhp4Yow==", "license": "MIT", "dependencies": {"undici-types": "~7.10.0"}}, "node_modules/@types/react": {"version": "19.1.10", "dev": true, "license": "MIT", "peer": true, "dependencies": {"csstype": "^3.0.2"}}, "node_modules/@types/selenium-webdriver": {"version": "4.1.28", "license": "MIT", "dependencies": {"@types/node": "*", "@types/ws": "*"}}, "node_modules/@types/ws": {"version": "8.18.1", "license": "MIT", "dependencies": {"@types/node": "*"}}, "node_modules/bun-types": {"version": "1.2.20", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*"}, "peerDependencies": {"@types/react": "^19"}}, "node_modules/core-util-is": {"version": "1.0.3", "license": "MIT"}, "node_modules/csstype": {"version": "3.1.3", "dev": true, "license": "MIT", "peer": true}, "node_modules/immediate": {"version": "3.0.6", "license": "MIT"}, "node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "node_modules/isarray": {"version": "1.0.0", "license": "MIT"}, "node_modules/jszip": {"version": "3.10.1", "license": "(MIT OR GPL-3.0-or-later)", "dependencies": {"lie": "~3.3.0", "pako": "~1.0.2", "readable-stream": "~2.3.6", "setimmediate": "^1.0.5"}}, "node_modules/lie": {"version": "3.3.0", "license": "MIT", "dependencies": {"immediate": "~3.0.5"}}, "node_modules/pako": {"version": "1.0.11", "license": "(MIT AND Zlib)"}, "node_modules/process-nextick-args": {"version": "2.0.1", "license": "MIT"}, "node_modules/readable-stream": {"version": "2.3.8", "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "node_modules/safe-buffer": {"version": "5.1.2", "license": "MIT"}, "node_modules/selenium-webdriver": {"version": "4.35.0", "funding": [{"type": "github", "url": "https://github.com/sponsors/SeleniumHQ"}, {"type": "opencollective", "url": "https://opencollective.com/selenium"}], "license": "Apache-2.0", "dependencies": {"@bazel/runfiles": "^6.3.1", "jszip": "^3.10.1", "tmp": "^0.2.3", "ws": "^8.18.2"}, "engines": {"node": ">= 20.0.0"}}, "node_modules/setimmediate": {"version": "1.0.5", "license": "MIT"}, "node_modules/string_decoder": {"version": "1.1.1", "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "node_modules/tmp": {"version": "0.2.5", "license": "MIT", "engines": {"node": ">=14.14"}}, "node_modules/typescript": {"version": "5.9.2", "license": "Apache-2.0", "peer": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "7.10.0", "license": "MIT"}, "node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "node_modules/ws": {"version": "8.18.3", "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}}}