// Node.js File Operations Cheatsheet & Demo Script
// ES2024 format with async/await

import { promises as fs } from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get current directory (ES module equivalent of __dirname)
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// ===========================================
// BASIC FILE & FOLDER OPERATIONS CHEATSHEET
// ===========================================

class FileManager {
  public readonly baseDir: string;

  constructor(baseDir: string = './demo') {
    this.baseDir = baseDir;
  }

  // 📁 CREATE FOLDER
  async createFolder(folderPath) {
    try {
      await fs.mkdir(path.join(this.baseDir, folderPath), { recursive: true });
      console.log(`✅ Folder created: ${folderPath}`);
    } catch (error) {
      console.error(`❌ Create folder failed: ${error.message}`);
    }
  }

  // 📄 CREATE & WRITE FILE
  async createFile(filePath, content = '') {
    try {
      const fullPath = path.join(this.baseDir, filePath);
      await fs.writeFile(fullPath, content, 'utf8');
      console.log(`✅ File created: ${filePath}`);
    } catch (error) {
      console.error(`❌ Create file failed: ${error.message}`);
    }
  }

  // ✏️ APPEND TO FILE
  async appendToFile(filePath, content) {
    try {
      const fullPath = path.join(this.baseDir, filePath);
      await fs.appendFile(fullPath, content, 'utf8');
      console.log(`✅ Content appended to: ${filePath}`);
    } catch (error) {
      console.error(`❌ Append failed: ${error.message}`);
    }
  }

  // 📖 READ FILE
  async readFile(filePath) {
    try {
      const fullPath = path.join(this.baseDir, filePath);
      const data = await fs.readFile(fullPath, 'utf8');
      console.log(`✅ File read: ${filePath}`);
      return data;
    } catch (error) {
      console.error(`❌ Read failed: ${error.message}`);
      return null;
    }
  }

  // 📋 LIST DIRECTORY CONTENTS
  async listDirectory(dirPath = '') {
    try {
      const fullPath = path.join(this.baseDir, dirPath);
      const items = await fs.readdir(fullPath, { withFileTypes: true });
      
      const result = {
        folders: items.filter(item => item.isDirectory()).map(item => item.name),
        files: items.filter(item => item.isFile()).map(item => item.name)
      };
      
      console.log(`✅ Directory listed: ${dirPath || 'root'}`);
      return result;
    } catch (error) {
      console.error(`❌ List directory failed: ${error.message}`);
      return { folders: [], files: [] };
    }
  }

  // ❓ CHECK IF EXISTS
  async exists(itemPath) {
    try {
      const fullPath = path.join(this.baseDir, itemPath);
      await fs.access(fullPath);
      return true;
    } catch {
      return false;
    }
  }

  // 📊 GET FILE STATS
  async getStats(itemPath) {
    try {
      const fullPath = path.join(this.baseDir, itemPath);
      const stats = await fs.stat(fullPath);
      return {
        isFile: stats.isFile(),
        isDirectory: stats.isDirectory(),
        size: stats.size,
        created: stats.birthtime,
        modified: stats.mtime
      };
    } catch (error) {
      console.error(`❌ Get stats failed: ${error.message}`);
      return null;
    }
  }

  // 📁➡️📁 COPY FILE/FOLDER
  async copy(source, destination) {
    try {
      const srcPath = path.join(this.baseDir, source);
      const destPath = path.join(this.baseDir, destination);
      await fs.cp(srcPath, destPath, { recursive: true });
      console.log(`✅ Copied: ${source} → ${destination}`);
    } catch (error) {
      console.error(`❌ Copy failed: ${error.message}`);
    }
  }

  // 📁➡️📁 MOVE/RENAME
  async move(source, destination) {
    try {
      const srcPath = path.join(this.baseDir, source);
      const destPath = path.join(this.baseDir, destination);
      await fs.rename(srcPath, destPath);
      console.log(`✅ Moved: ${source} → ${destination}`);
    } catch (error) {
      console.error(`❌ Move failed: ${error.message}`);
    }
  }

  // 🗑️ DELETE FILE
  async deleteFile(filePath) {
    try {
      const fullPath = path.join(this.baseDir, filePath);
      await fs.unlink(fullPath);
      console.log(`✅ File deleted: ${filePath}`);
    } catch (error) {
      console.error(`❌ Delete file failed: ${error.message}`);
    }
  }

  // 🗑️ DELETE FOLDER
  async deleteFolder(folderPath) {
    try {
      const fullPath = path.join(this.baseDir, folderPath);
      await fs.rm(fullPath, { recursive: true, force: true });
      console.log(`✅ Folder deleted: ${folderPath}`);
    } catch (error) {
      console.error(`❌ Delete folder failed: ${error.message}`);
    }
  }

  // 🧹 CLEANUP (delete base directory)
  async cleanup() {
    try {
      await fs.rm(this.baseDir, { recursive: true, force: true });
      console.log(`✅ Cleanup completed: ${this.baseDir}`);
    } catch (error) {
      console.error(`❌ Cleanup failed: ${error.message}`);
    }
  }
}

// ===========================================
// JSON DATA OPERATIONS
// ===========================================

class JSONManager {
  static async readJSON(filePath) {
    try {
      const data = await fs.readFile(filePath, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error(`❌ Read JSON failed: ${error.message}`);
      return null;
    }
  }

  static async writeJSON(filePath, data, indent = 2) {
    try {
      const jsonString = JSON.stringify(data, null, indent);
      await fs.writeFile(filePath, jsonString, 'utf8');
      console.log(`✅ JSON written: ${filePath}`);
    } catch (error) {
      console.error(`❌ Write JSON failed: ${error.message}`);
    }
  }

  static async updateJSON(filePath, updater) {
    try {
      const data = await this.readJSON(filePath);
      if (data) {
        const updated = updater(data);
        await this.writeJSON(filePath, updated);
        console.log(`✅ JSON updated: ${filePath}`);
      }
    } catch (error) {
      console.error(`❌ Update JSON failed: ${error.message}`);
    }
  }
}

// ===========================================
// DEMO SCRIPT - SHOWS ALL OPERATIONS
// ===========================================

async function runDemo() {
  console.log('🚀 Starting Node.js File Operations Demo\n');
  
  const fm = new FileManager('./demo-files');
  
  try {
    // 1. Create project structure
    console.log('📁 Creating project structure...');
    await fm.createFolder('src');
    await fm.createFolder('data');
    await fm.createFolder('logs');
    
    // 2. Create files with content
    console.log('\n📄 Creating files...');
    await fm.createFile('README.md', '# My Project\nThis is a demo project.');
    await fm.createFile('src/index.js', 'console.log("Hello World!");');
    await fm.createFile('data/users.json', '[]');
    
    // 3. Work with JSON data
    console.log('\n📊 Working with JSON...');
    const users = [
      { id: 1, name: 'John', email: '<EMAIL>' },
      { id: 2, name: 'Jane', email: '<EMAIL>' }
    ];
    await JSONManager.writeJSON(path.join(fm.baseDir, 'data/users.json'), users);
    
    // 4. Read and display file contents
    console.log('\n📖 Reading files...');
    const readme = await fm.readFile('README.md');
    console.log('README.md contents:', readme);
    
    const userData = await JSONManager.readJSON(path.join(fm.baseDir, 'data/users.json'));
    console.log('Users data:', userData);
    
    // 5. Append to files
    console.log('\n✏️ Appending content...');
    await fm.appendToFile('README.md', '\n\n## Features\n- File operations\n- JSON handling');
    await fm.appendToFile('logs/app.log', `${new Date().toISOString()} - App started\n`);
    
    // 6. List directory contents
    console.log('\n📋 Directory structure:');
    const rootContents = await fm.listDirectory();
    console.log('Root:', rootContents);
    
    const srcContents = await fm.listDirectory('src');
    console.log('src/', srcContents);
    
    // 7. File operations
    console.log('\n🔄 File operations...');
    await fm.copy('README.md', 'README-backup.md');
    
    // Check if file exists
    const exists = await fm.exists('README-backup.md');
    console.log('Backup exists:', exists);
    
    // Get file stats
    const stats = await fm.getStats('README.md');
    console.log('README.md stats:', stats);
    
    // 8. Update JSON data
    console.log('\n🔄 Updating JSON...');
    await JSONManager.updateJSON(
      path.join(fm.baseDir, 'data/users.json'),
      (data) => {
        data.push({ id: 3, name: 'Bob', email: '<EMAIL>' });
        return data;
      }
    );
    
    // 9. Show final state
    console.log('\n📋 Final directory contents:');
    const finalContents = await fm.listDirectory();
    console.log(finalContents);
    
    console.log('\n✅ Demo completed successfully!');
    console.log('\n🧹 Cleaning up in 8 seconds...');
    
    // Cleanup after 3 seconds
    setTimeout(async () => {
      await fm.cleanup();
      console.log('Demo files cleaned up.');
    }, 8000);
    
  } catch (error) {
    console.error('❌ Demo failed:', error);
  }
}

// ===========================================
// QUICK REFERENCE CHEATSHEET
// ===========================================

const CHEATSHEET = `
🗂️  NODE.JS FILE OPERATIONS CHEATSHEET
=======================================

📁 FOLDERS:
await fs.mkdir(path, { recursive: true })     // Create folder(s)
await fs.rmdir(path)                          // Delete empty folder
await fs.rm(path, { recursive: true })       // Delete folder + contents

📄 FILES:
await fs.writeFile(path, content)            // Create/overwrite file
await fs.appendFile(path, content)           // Append to file
await fs.readFile(path, 'utf8')              // Read file as string
await fs.unlink(path)                        // Delete file

🔍 CHECK & INFO:
await fs.access(path)                        // Check if exists
await fs.stat(path)                          // Get file/folder info
await fs.readdir(path)                       // List directory contents

🔄 COPY & MOVE:
await fs.cp(src, dest, { recursive: true })  // Copy file/folder
await fs.rename(src, dest)                   // Move/rename

📊 JSON:
JSON.parse(await fs.readFile(path, 'utf8'))  // Read JSON
await fs.writeFile(path, JSON.stringify(obj)) // Write JSON

🛠️ PATH UTILITIES:
path.join(dir, file)                         // Join paths safely
path.dirname(filepath)                       // Get directory
path.basename(filepath)                      // Get filename
path.extname(filepath)                       // Get extension

⚠️ ALWAYS USE:
- 'utf8' encoding for text files
- { recursive: true } for nested operations
- try/catch for error handling
- path.join() instead of string concatenation
`;

// Run demo if this file is executed directly
if (import.meta.main) {
  console.log(CHEATSHEET);
  console.log('\n' + '='.repeat(50));
  runDemo();
}

// Export for use in other files
export { FileManager, JSONManager, CHEATSHEET };